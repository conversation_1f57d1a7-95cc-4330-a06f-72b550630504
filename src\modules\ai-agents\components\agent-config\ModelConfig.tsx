import { Checkbox, Icon, Select, Slider, Textarea, Typography, Card } from '@/shared/components/common';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  normalizeModelConfig
} from '../../hooks/useBaseModel';
import { apiClient } from '@/shared/api/axios';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import { useAgentBasicInfo } from '../../hooks/useAgentBasicInfo';

import { TypeProviderEnum } from '../../types';



interface ModelConfigData {
  provider: TypeProviderEnum | 'redai'; // Thêm 'redai' cho provider mặc định
  providerId?: string; // ID của user provider (nếu không phải RedAI)
  keyLlmId?: string | undefined; // ID của key LLM được chọn
  modelId: string;
  vectorStore: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
  instruction?: string;
  showAdvancedConfig?: boolean; // Thêm field để track trạng thái advanced config
}

interface ModelConfigProps {
  initialData?: ModelConfigData;
  onSave?: (data: ModelConfigData) => void;
  agentId?: string; // Thêm agentId để load và save data
  mode?: 'create' | 'edit'; // Thêm mode để phân biệt create/edit
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

// Interface cho Key LLM response
interface KeyLlmItem {
  id: string;
  name: string;
  provider: string;
  createdAt: string;
  updatedAt: string;
}

interface KeyLlmResponse {
  items: KeyLlmItem[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

// Interface cho Model response
interface ModelItem {
  id: string;
  modelId?: string;
  model_id?: string;
  name?: string;
  config?: unknown;
}

/**
 * Component cấu hình model AI cho Agent
 */
const ModelConfig: React.FC<ModelConfigProps> = ({
  initialData,
  onSave,
  agentId,
  mode = 'create'
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // API hooks để load basic info
  const { data: basicInfoResponse } = useAgentBasicInfo(agentId && mode === 'edit' ? agentId : '');

  const [configData, setConfigData] = useState<ModelConfigData>(initialData || {
    provider: 'redai', // Mặc định sử dụng RedAI
    modelId: 'gpt-4',
    vectorStore: 'pinecone',
    maxTokens: 1000, // Giá trị mặc định khi không check advanced config
    temperature: 1,
    topP: 0.5,
    topK: 20,
    instruction: ''
  });



  // State để kiểm soát hiển thị cấu hình nâng cao
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);

  // Load data từ API khi có agentId và mode là edit
  useEffect(() => {
    if (basicInfoResponse && mode === 'edit') {
      const apiData = basicInfoResponse;
      const maxTokens = apiData.modelConfig?.max_tokens || 1000;
      const temperature = apiData.modelConfig?.temperature || 1;
      const topP = apiData.modelConfig?.top_p || 0.5;
      const topK = 20; // Không có trong API response, dùng default

      // Kiểm tra xem có advanced config không (nếu khác giá trị mặc định thì có advanced config)
      const hasAdvancedConfig = maxTokens !== 1000 || temperature !== 1 || topP !== 0.5 || topK !== 20;

      const newConfigData: ModelConfigData = {
        provider: (apiData.provider as TypeProviderEnum) || 'redai',
        modelId: apiData.modelId || 'gpt-4',
        vectorStore: 'pinecone', // Default value
        maxTokens: maxTokens,
        temperature: temperature,
        topP: topP,
        topK: topK,
        instruction: apiData.instruction || '',
        showAdvancedConfig: hasAdvancedConfig
      };

      setConfigData(newConfigData);
      setShowAdvancedConfig(hasAdvancedConfig);
    }
  }, [basicInfoResponse, mode]);







  // State cho Key LLM
  const [keyLlmOptions, setKeyLlmOptions] = useState<{ value: string; label: string }[]>([]);
  const [isLoadingKeyLlm, setIsLoadingKeyLlm] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(TypeProviderEnum.OPENAI);

  // State cho Models
  const [modelOptions, setModelOptions] = useState<{ value: string; label: string; data?: unknown }[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [modelsError, setModelsError] = useState<string | null>(null);

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Load Key LLM options
  const loadKeyLlmOptions = useCallback(async () => {
    setIsLoadingKeyLlm(true);
    try {
      const response = await apiClient.get('/key-llm', {
        params: {
          page: 1,
          limit: 100,
          provider: selectedProvider
        }
      });

      const keyLlmData = response.result as KeyLlmResponse;
      const options = [
        { value: 'redai', label: 'RedAI' },
        ...(keyLlmData.items || []).map(item => ({
          value: item.id,
          label: item.name
        }))
      ];

      setKeyLlmOptions(options);
    } catch (error) {
      console.error('Error loading key LLM options:', error);
      setKeyLlmOptions([{ value: 'redai', label: 'RedAI' }]);
    } finally {
      setIsLoadingKeyLlm(false);
    }
  }, [selectedProvider]);

  // Load models based on selected key LLM
  const loadModels = useCallback(async () => {
    if (!configData.keyLlmId) return [];

    try {
      let endpoint = '';
      const params: { page: number; limit: number; provider?: TypeProviderEnum } = { page: 1, limit: 100 };

      if (configData.keyLlmId === 'redai') {
        endpoint = '/models/system-models';
        // Truyền provider đã chọn khi chọn RedAI
        params.provider = selectedProvider;
      } else {
        endpoint = `/models/user-models-by-keys/${configData.keyLlmId}`;
      }

      const response = await apiClient.get(endpoint, { params });

      const modelsData = response.result as unknown;

      // Handle different response structures
      let items: ModelItem[] = [];
      if (Array.isArray(modelsData)) {
        items = modelsData as ModelItem[];
      } else if (modelsData && typeof modelsData === 'object' && 'items' in modelsData) {
        const responseWithItems = modelsData as { items: ModelItem[] };
        if (Array.isArray(responseWithItems.items)) {
          items = responseWithItems.items;
        }
      }

      return items.map((model: ModelItem) => ({
        value: model.id,
        label: model.modelId || model.model_id || model.name || 'Unknown Model',
        data: model
      }));

    } catch (error) {
      console.error('Error loading models:', error);
      throw error;
    }
  }, [configData.keyLlmId, selectedProvider]);

  // Load models when keyLlmId changes or selectedProvider changes (for RedAI)
  useEffect(() => {
    const loadModelsData = async () => {
      if (!configData.keyLlmId) {
        setModelOptions([]);
        setModelsError(null);
        return;
      }

      setIsLoadingModels(true);
      setModelsError(null);
      try {
        const models = await loadModels();
        setModelOptions(models);
      } catch (error) {
        console.error('Error loading models:', error);
        setModelOptions([]);
        setModelsError('Lỗi tải model');
      } finally {
        setIsLoadingModels(false);
      }
    };

    loadModelsData();
  }, [configData.keyLlmId, loadModels]);

  // Fallback options nếu API chưa load hoặc lỗi
  const fallbackOptions = useMemo(() => [
    {
      value: 'loading',
      label: isLoadingModels ? 'Đang tải...' : modelsError ? 'Lỗi tải model' : 'Không có model'
    }
  ], [isLoadingModels, modelsError]);

  // Lấy config của model hiện tại được chọn
  const selectedModelConfig = useMemo(() => {
    if (!configData.modelId || !modelOptions.length) {
      return null;
    }

    const selectedModel = modelOptions.find(option => option.value === configData.modelId);
    if (!selectedModel?.data) {
      return null;
    }

    const rawConfig = (selectedModel.data as ModelItem)?.config || null;
    const normalizedConfig = normalizeModelConfig(rawConfig);

    return normalizedConfig;
  }, [configData.modelId, modelOptions]);

  // Load key LLM options when provider changes
  useEffect(() => {
    loadKeyLlmOptions();
  }, [loadKeyLlmOptions]);

  // Auto-update model khi có data từ API và model hiện tại không có trong danh sách
  useEffect(() => {
    if (modelOptions.length > 0) {
      const currentModelExists = modelOptions.some(option => option.value === configData.modelId);
      if (!currentModelExists) {
        // Chọn model đầu tiên trong danh sách
        const firstModel = modelOptions[0];
        if (firstModel) {
          const newConfigData = {
            ...configData,
            modelId: firstModel.value
          };

          setConfigData(newConfigData);
          if (onSave) onSave(newConfigData);
        }
      }
    }
  }, [modelOptions, configData, onSave]);



  // Xử lý khi thay đổi select
  const handleSelectChange = (name: string, value: string | number | string[] | number[]) => {
    const newData = {
      ...configData,
      [name]: value
    };

    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  // Xử lý khi thay đổi slider
  const handleSliderChange = (name: string, value: number) => {
    const newConfigData = {
      ...configData,
      [name]: value
    };

    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };

  // Xử lý khi thay đổi textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const newConfigData = {
      ...configData,
      [name]: value
    };

    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };



  // Xử lý khi thay đổi checkbox
  const handleCheckboxChange = (checked: boolean) => {
    setShowAdvancedConfig(checked);

    // Cập nhật configData với showAdvancedConfig
    // Nếu không check advanced config, sử dụng giá trị mặc định
    const newConfigData = {
      ...configData,
      showAdvancedConfig: checked,
      // Khi không check advanced config, sử dụng giá trị mặc định
      maxTokens: checked ? configData.maxTokens : 1000,
      temperature: checked ? configData.temperature : 1,
      topP: checked ? configData.topP : 0.5,
      topK: checked ? configData.topK : 20,
    };
    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };



  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);

    // Nếu đang chọn RedAI, chỉ reset model, không reset keyLlmId
    // Nếu chưa chọn keyLlmId, reset tất cả
    const newConfigData = {
      ...configData,
      provider,
      keyLlmId: configData.keyLlmId || undefined,
      modelId: '' // Reset model khi thay đổi provider
    };

    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };

  // Handle key LLM selection
  const handleKeyLlmChange = (keyLlmId: string | number | string[] | number[]) => {
    const selectedKeyLlmId = (Array.isArray(keyLlmId) ? keyLlmId[0] : keyLlmId) as string;

    const newConfigData = {
      ...configData,
      keyLlmId: selectedKeyLlmId,
      modelId: '' // Reset model khi chọn key LLM mới
    };

    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };

  return (
    <div>
      <div className="p-4 space-y-6">
        {/* Provider selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
            {providers.map((provider) => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={false}
              />
            ))}
          </div>
        </div>

        {/* Key LLM selection */}
        <div className="mb-6">
          <label htmlFor="keyLlmId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Key LLM
          </label>
          <Select
            options={keyLlmOptions}
            value={configData.keyLlmId || ''}
            onChange={handleKeyLlmChange}
            placeholder="Chọn Key LLM"
            disabled={isLoadingKeyLlm}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
          <div>
            <label htmlFor="modelId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Model
            </label>
            <Select
              options={modelOptions.length > 0 ? modelOptions : fallbackOptions}
              value={configData.modelId}
              onChange={(value) => handleSelectChange('modelId', value)}
              placeholder="Chọn model"
              disabled={isLoadingModels}
            />
          </div>

          {/* Vector Store - chỉ hiển thị khi model support file_search */}
          {selectedModelConfig?.file_search === true && (
            <div>
              <label htmlFor="vectorStore" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Vector Store
              </label>
              <Select
                options={[
                  { value: 'pinecone', label: 'Pinecone' },
                  { value: 'qdrant', label: 'Qdrant' },
                  { value: 'weaviate', label: 'Weaviate' },
                  { value: 'chroma', label: 'Chroma' }
                ]}
                value={configData.vectorStore}
                onChange={(value) => handleSelectChange('vectorStore', value)}
                placeholder="Chọn vector store"
              />
            </div>
          )}
        </div>

        {/* Instruction textarea */}
        <div className="mb-6">
          <label htmlFor="instruction" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Instruction
          </label>
          <Textarea
            id="instruction"
            name="instruction"
            value={configData.instruction || ''}
            onChange={handleTextareaChange}
            placeholder="Nhập hướng dẫn cho model..."
            className="w-full"
            rows={4}
          />
        </div>

        {/* Advanced configuration checkbox */}
        <div className="mb-4">
          <Checkbox
            label="Tùy chỉnh nâng cao"
            checked={showAdvancedConfig}
            onChange={handleCheckboxChange}
          />
        </div>

        {/* Advanced configuration sliders */}
        {showAdvancedConfig && (
          <div className="space-y-6 border-t pt-4 mt-4">
            {/* Max Tokens - luôn hiển thị */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Max Tokens
              </label>
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Số lượng token tối đa cho mỗi lần gọi API
              </div>
              <Slider
                value={configData.maxTokens}
                min={100}
                max={4096}
                step={1}
                onValueChange={(value: number) => handleSliderChange('maxTokens', value)}
                valueSuffix=""
              />
            </div>

            {/* Temperature - chỉ hiển thị khi model support */}
            {showAdvancedConfig && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Temperature
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Mức độ ngẫu nhiên trong kết quả (0-2)
                </div>
                <Slider
                  value={configData.temperature}
                  min={0}
                  max={2}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('temperature', value)}
                />
              </div>
            )}

            {/* Top P - chỉ hiển thị khi model support */}
            {showAdvancedConfig && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top P
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Xác suất tích lũy cho lựa chọn token (0-1)
                </div>
                <Slider
                  value={configData.topP}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('topP', value)}
                />
              </div>
            )}

            {/* Top K - chỉ hiển thị khi model support */}
            {showAdvancedConfig && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top K
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Số lượng token có xác suất cao nhất để xem xét
                </div>
                <Slider
                  value={configData.topK}
                  min={1}
                  max={50}
                  step={1}
                  onValueChange={(value: number) => handleSliderChange('topK', value)}
                />
              </div>
            )}
          </div>
        )}



      </div>
    </div>
  );
};

export default ModelConfig;
