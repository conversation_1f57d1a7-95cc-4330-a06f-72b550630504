import { apiClient } from '@/shared/api/axios';

// Types cho Basic Info
export interface AgentBasicInfoDto {
  id?: string;
  avatar: string;
  name: string;
  typeAgentId?: number;
  provider: string;
  modelId: string;
  modelConfig: {
    temperature: number;
    top_p: number;
    top_k: number;
    max_tokens: number;
  };
  instruction: string;
  updatedAt: string;
}

// Response wrapper cho update - có thêm urlUpload
export interface AgentBasicInfoUpdateResponse extends AgentBasicInfoDto {
  urlUpload?: string; // URL để upload avatar lên S3
}

// Response wrapper - same as AgentBasicInfoDto cho get
export type AgentBasicInfoResponse = AgentBasicInfoDto;

export interface UpdateAgentBasicInfoDto {
  name: string;
  avatarFile?: {
    fileName: string;
    mimeType: string;
  } | null;
  userModelId?: string | null;
  keyLlmId?: string | null;
  systemModelId?: string | null;
  modelFineTuneId?: string | null;
  modelConfig: {
    temperature: number;
    top_p: number;
    top_k: number;
    max_tokens: number;
  };
  instruction: string;
  vectorStoreId?: string | null;
}

// API functions
export const getAgentBasicInfo = async (agentId: string): Promise<AgentBasicInfoResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/basic-info`);
  console.log('getAgentBasicInfo - Raw response:', response);
  return response.result as AgentBasicInfoResponse; // apiClient.get đã trả về { code, message, result }
};

export const updateAgentBasicInfo = async (
  agentId: string,
  data: UpdateAgentBasicInfoDto
): Promise<AgentBasicInfoUpdateResponse> => {
  console.log('updateAgentBasicInfo - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/basic-info`,
    data
  });

  const response = await apiClient.put(`/user/agents/${agentId}/basic-info`, data);
  console.log('updateAgentBasicInfo - API response:', response);
  return response.result as AgentBasicInfoUpdateResponse; // apiClient.put đã trả về { code, message, result }
};
