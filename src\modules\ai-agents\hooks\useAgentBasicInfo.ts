import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAgentBasicInfo,
  updateAgentBasicInfo,
  // AgentBasicInfoDto, // Unused for now
  UpdateAgentBasicInfoDto,
  AgentBasicInfoUpdateResponse
} from '../api/agent-basic-info.api';

// Query key factory
export const agentBasicInfoKeys = {
  all: ['agent-basic-info'] as const,
  detail: (agentId: string) => [...agentBasicInfoKeys.all, agentId] as const,
};

// Hook để lấy basic info
export const useAgentBasicInfo = (agentId: string) => {
  const query = useQuery({
    queryKey: agentBasicInfoKeys.detail(agentId),
    queryFn: async () => {
      console.log('useAgentBasicInfo - Calling API for agentId:', agentId);
      try {
        const result = await getAgentBasicInfo(agentId);
        console.log('useAgentBasicInfo - API success:', result);
        console.log('useAgentBasicInfo - Result type:', typeof result);
        console.log('useAgentBasicInfo - Result keys:', result ? Object.keys(result) : 'null');
        return result;
      } catch (error) {
        console.error('useAgentBasicInfo - API error:', error);
        throw error;
      }
    },
    enabled: !!agentId,
  });

  console.log('useAgentBasicInfo - Query state:', {
    agentId,
    isLoading: query.isLoading,
    data: query.data,
    error: query.error,
    isError: query.isError
  });

  return query;
};

// Hook để update basic info
export const useUpdateAgentBasicInfo = () => {
  const queryClient = useQueryClient();

  return useMutation<
    AgentBasicInfoUpdateResponse,
    Error,
    { agentId: string; data: UpdateAgentBasicInfoDto }
  >({
    mutationFn: ({ agentId, data }: { agentId: string; data: UpdateAgentBasicInfoDto }) =>
      updateAgentBasicInfo(agentId, data),
    onSuccess: (data, variables) => {
      // Invalidate và update cache
      queryClient.invalidateQueries({
        queryKey: agentBasicInfoKeys.detail(variables.agentId),
      });

      // Update cache trực tiếp với data không có urlUpload
      const { urlUpload: _urlUpload, ...basicInfoData } = data;
      queryClient.setQueryData(
        agentBasicInfoKeys.detail(variables.agentId),
        basicInfoData
      );
    },
  });
};
